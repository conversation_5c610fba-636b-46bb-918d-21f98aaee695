<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chart Loader Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-card {
            background: white;
            border-radius: 14px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .chart-container {
            width: 100%;
            height: 340px;
            position: relative;
            background: #f9f9f9;
            border: 2px dashed #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            margin-top: 16px;
        }
        .controls {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }
        .btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
        }
        .btn:hover {
            background: #f5f5f5;
        }
        .dropdown {
            position: relative;
            display: inline-block;
        }
        .dropdown-content {
            display: none;
            position: absolute;
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            min-width: 120px;
            top: 100%;
            left: 0;
        }
        .dropdown-content.show {
            display: block;
        }
        .dropdown-item {
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
        }
        .dropdown-item:hover {
            background: #f5f5f5;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Chart Loader Test</h1>
        <p>Test the snap loader implementation for chart operations</p>

        <!-- Monthly Sales Chart Test -->
        <div class="test-card">
            <h3>Monthly Sales Chart</h3>
            <div class="controls">
                <button class="btn" onclick="testChartInit('monthly')">Initialize Chart</button>
                <button class="btn" onclick="testCompareMode('monthly')">Toggle Compare</button>
                <div class="dropdown">
                    <button class="btn" onclick="toggleDropdown('yearDropdown')">Year: 2025 ▼</button>
                    <div id="yearDropdown" class="dropdown-content">
                        <div class="dropdown-item" onclick="testYearChange(2025)">2025</div>
                        <div class="dropdown-item" onclick="testYearChange(2024)">2024</div>
                        <div class="dropdown-item" onclick="testYearChange(2023)">2023</div>
                    </div>
                </div>
            </div>
            <div id="monthly-sales-chart-container" class="chart-container monthly-sales-chart-container">
                <span>Monthly Sales Chart Area</span>
            </div>
        </div>

        <!-- Last Week Chart Test -->
        <div class="test-card">
            <h3>Last Week's Sales Chart</h3>
            <div class="controls">
                <button class="btn" onclick="testChartInit('lastweek')">Initialize Chart</button>
                <button class="btn" onclick="testCompareMode('lastweek')">Toggle Compare</button>
            </div>
            <div id="last-week-chart-container" class="chart-container last-week-chart-container">
                <span>Last Week's Chart Area</span>
            </div>
        </div>

        <!-- Today vs Previous Years Test -->
        <div class="test-card">
            <h3>Today vs Previous Years Chart</h3>
            <div class="controls">
                <button class="btn" onclick="testChartInit('today')">Initialize Chart</button>
            </div>
            <div id="today-vs-previous-years-chart-container" class="chart-container today-vs-previous-years-chart-container">
                <span>Today vs Previous Years Chart Area</span>
            </div>
        </div>
    </div>

    <script src="snapapp.js"></script>
    <script>
        function testChartInit(chartType) {
            let containerId, loaderId, message;
            
            switch(chartType) {
                case 'monthly':
                    containerId = '#monthly-sales-chart-container';
                    loaderId = 'monthly-sales-chart-loader';
                    message = 'Loading chart data...';
                    break;
                case 'lastweek':
                    containerId = '#last-week-chart-container';
                    loaderId = 'last-week-chart-loader';
                    message = 'Loading chart data...';
                    break;
                case 'today':
                    containerId = '#today-vs-previous-years-chart-container';
                    loaderId = 'today-vs-previous-years-loader';
                    message = 'Loading chart data...';
                    break;
            }

            const container = document.querySelector(containerId);
            if (container && window.SnapLoader) {
                // Show loader
                window.SnapLoader.showOverlay(container, {
                    text: message,
                    id: loaderId
                });

                // Simulate chart initialization (2 seconds)
                setTimeout(() => {
                    window.SnapLoader.hideOverlay(container);
                }, 2000);
            }
        }

        function testCompareMode(chartType) {
            let containerId, loaderId;
            
            switch(chartType) {
                case 'monthly':
                    containerId = '#monthly-sales-chart-container';
                    loaderId = 'monthly-sales-compare-loader';
                    break;
                case 'lastweek':
                    containerId = '#last-week-chart-container';
                    loaderId = 'last-week-compare-loader';
                    break;
            }

            const container = document.querySelector(containerId);
            if (container && window.SnapLoader) {
                // Show loader
                window.SnapLoader.showOverlay(container, {
                    text: 'Updating comparison...',
                    id: loaderId
                });

                // Simulate compare mode update (1 second)
                setTimeout(() => {
                    window.SnapLoader.hideOverlay(container);
                }, 1000);
            }
        }

        function testYearChange(year) {
            const container = document.querySelector('#monthly-sales-chart-container');
            if (container && window.SnapLoader) {
                // Show loader
                window.SnapLoader.showOverlay(container, {
                    text: 'Loading year data...',
                    id: 'monthly-sales-year-loader'
                });

                // Update dropdown text
                document.querySelector('.dropdown .btn').textContent = `Year: ${year} ▼`;
                
                // Hide dropdown
                document.getElementById('yearDropdown').classList.remove('show');

                // Simulate year change (1.5 seconds)
                setTimeout(() => {
                    window.SnapLoader.hideOverlay(container);
                }, 1500);
            }
        }

        function toggleDropdown(dropdownId) {
            const dropdown = document.getElementById(dropdownId);
            dropdown.classList.toggle('show');
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.matches('.dropdown .btn')) {
                const dropdowns = document.querySelectorAll('.dropdown-content');
                dropdowns.forEach(dropdown => {
                    dropdown.classList.remove('show');
                });
            }
        });
    </script>
</body>
</html>
